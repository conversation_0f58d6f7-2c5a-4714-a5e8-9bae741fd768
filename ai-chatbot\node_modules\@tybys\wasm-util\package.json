{"name": "@tybys/wasm-util", "version": "0.10.0", "description": "WASI polyfill for browser and some wasm util", "main": "./lib/cjs/index.js", "module": "./dist/wasm-util.esm-bundler.js", "types": "./dist/wasm-util.d.ts", "exports": {".": {"module": "./dist/wasm-util.esm-bundler.js", "import": "./lib/mjs/index.mjs", "require": "./lib/cjs/index.js", "types": "./dist/wasm-util.d.ts"}}, "scripts": {"build": "tsgo build", "watch": "tsgo watch", "test": "jest", "lint": "eslint ./src/**/*.{ts,js} --fix", "prepare": "npm run build"}, "publishConfig": {"access": "public"}, "keywords": ["wasm", "webassembly", "wasi", "polyfill"], "repository": {"type": "git", "url": "https://github.com/toyobayashi/wasm-util.git"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"tslib": "^2.4.0"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "memfs-browser": "^3.4.13000", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}}