'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Menu, Download, Trash2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ChatMessage from '@/components/chat/ChatMessage';
import ChatInput from '@/components/chat/ChatInput';
import ChatSidebar from '@/components/chat/ChatSidebar';
import GuestBanner from '@/components/chat/GuestBanner';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ChatSession {
  _id: string;
  title: string;
  updatedAt: string;
  messageCount: number;
}

export default function ChatPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [messages, setMessages] = useState<Message[]>([]);
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [isGuest, setIsGuest] = useState(false);
  const [guestRemainingRequests, setGuestRemainingRequests] = useState(10);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Check for guest mode
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const guestMode = urlParams.get('guest') === 'true';
    setIsGuest(guestMode);

    // Load guest messages from localStorage
    if (guestMode) {
      const savedMessages = localStorage.getItem('guestMessages');
      if (savedMessages) {
        setMessages(JSON.parse(savedMessages));
      }
    }
  }, []);

  // Redirect if not authenticated and not in guest mode
  useEffect(() => {
    if (status === 'unauthenticated' && !isGuest) {
      router.push('/auth/signin');
    }
  }, [status, router, isGuest]);

  // Load user preferences and sessions
  useEffect(() => {
    if (session?.user) {
      loadUserPreferences();
      loadSessions();
    }
  }, [session]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  // Apply theme
  useEffect(() => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [theme]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadUserPreferences = async () => {
    try {
      const response = await fetch('/api/user/preferences');
      if (response.ok) {
        const data = await response.json();
        setTheme(data.preferences.theme);
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
    }
  };

  const loadSessions = async () => {
    try {
      const response = await fetch('/api/sessions');
      if (response.ok) {
        const data = await response.json();
        setSessions(data.sessions);
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
      toast.error('Failed to load chat history');
    }
  };

  const loadSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/sessions/${sessionId}`);
      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp),
        })));
        setCurrentSessionId(sessionId);
      }
    } catch (error) {
      console.error('Failed to load session:', error);
      toast.error('Failed to load chat session');
    }
  };

  const createNewChat = async () => {
    setMessages([]);
    setCurrentSessionId(null);
    setSidebarOpen(false);
  };

  const sendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return;

    const userMessage: Message = {
      role: 'user',
      content,
      timestamp: new Date(),
    };

    const newMessages = [...messages, userMessage];
    setMessages(newMessages);
    setIsLoading(true);
    setIsTyping(true);

    try {
      let response;

      if (isGuest) {
        // Guest mode API call
        response = await fetch('/api/chat/guest', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: content,
            conversationHistory: messages,
          }),
        });
      } else {
        // Authenticated user API call
        response = await fetch('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: content,
            sessionId: currentSessionId,
          }),
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 429 && isGuest) {
          toast.error('Rate limit reached! Sign up for unlimited access.');
        } else {
          throw new Error(errorData.error || 'Failed to send message');
        }
        return;
      }

      const data = await response.json();

      const assistantMessage: Message = {
        role: 'assistant',
        content: data.response,
        timestamp: new Date(),
      };

      const finalMessages = [...newMessages, assistantMessage];
      setMessages(finalMessages);

      // Update guest remaining requests
      if (isGuest && data.remaining !== undefined) {
        setGuestRemainingRequests(data.remaining);
      }

      // Save guest messages to localStorage
      if (isGuest) {
        localStorage.setItem('guestMessages', JSON.stringify(finalMessages));
      }

      // Update current session ID if it's a new chat (authenticated users only)
      if (!isGuest && !currentSessionId && data.sessionId) {
        setCurrentSessionId(data.sessionId);
        loadSessions(); // Refresh sessions list
      }

    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to send message');
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const deleteSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/sessions/${sessionId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setSessions(prev => prev.filter(s => s._id !== sessionId));
        if (currentSessionId === sessionId) {
          setMessages([]);
          setCurrentSessionId(null);
        }
        toast.success('Chat deleted successfully');
      }
    } catch (error) {
      console.error('Failed to delete session:', error);
      toast.error('Failed to delete chat');
    }
  };

  const renameSession = async (sessionId: string, newTitle: string) => {
    try {
      const response = await fetch(`/api/sessions/${sessionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title: newTitle }),
      });

      if (response.ok) {
        setSessions(prev => prev.map(s => 
          s._id === sessionId ? { ...s, title: newTitle } : s
        ));
        toast.success('Chat renamed successfully');
      }
    } catch (error) {
      console.error('Failed to rename session:', error);
      toast.error('Failed to rename chat');
    }
  };

  const toggleTheme = async () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    
    try {
      await fetch('/api/user/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ theme: newTheme }),
      });
    } catch (error) {
      console.error('Failed to update theme preference:', error);
    }
  };

  const exportChat = () => {
    if (messages.length === 0) {
      toast.error('No messages to export');
      return;
    }

    const chatText = messages.map(msg => 
      `${msg.role === 'user' ? 'You' : 'AI'} (${msg.timestamp.toLocaleString()}): ${msg.content}`
    ).join('\n\n');

    const blob = new Blob([chatText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Chat exported successfully');
  };

  const clearChat = () => {
    if (confirm('Are you sure you want to clear this chat?')) {
      setMessages([]);
      if (isGuest) {
        localStorage.removeItem('guestMessages');
      }
      toast.success('Chat cleared');
    }
  };

  if (status === 'loading' && !isGuest) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
        <div className="relative">
          <div className="animate-spin rounded-full h-32 w-32 border-4 border-indigo-200"></div>
          <div className="animate-spin rounded-full h-32 w-32 border-4 border-indigo-600 border-t-transparent absolute top-0 left-0"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <svg className="h-8 w-8 text-indigo-600 animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
        </div>
      </div>
    );
  }

  if (!session && !isGuest) {
    return null;
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar - Hide for guest users */}
      {!isGuest && (
        <ChatSidebar
          sessions={sessions}
          currentSessionId={currentSessionId || undefined}
          onSessionSelect={loadSession}
          onNewChat={createNewChat}
          onDeleteSession={deleteSession}
          onRenameSession={renameSession}
          isOpen={sidebarOpen}
          onToggle={() => setSidebarOpen(!sidebarOpen)}
          theme={theme}
          onThemeToggle={toggleTheme}
        />
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {!isGuest && (
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg"
                >
                  <Menu size={20} />
                </button>
              )}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {isGuest
                    ? 'Guest Chat'
                    : currentSessionId
                      ? sessions.find(s => s._id === currentSessionId)?.title || 'Chat'
                      : 'New Chat'
                  }
                </h2>
                {isGuest && (
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {guestRemainingRequests} messages remaining •
                    <a href="/auth/signup" className="text-indigo-600 hover:text-indigo-500 ml-1">
                      Sign up for unlimited
                    </a>
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {isGuest && (
                <a
                  href="/auth/signup"
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                >
                  Sign Up
                </a>
              )}
              <button
                onClick={exportChat}
                disabled={messages.length === 0}
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                title="Export chat"
              >
                <Download size={16} />
              </button>
              <button
                onClick={clearChat}
                disabled={messages.length === 0}
                className="p-2 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                title="Clear chat"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full text-center">
              <div className="max-w-md mx-auto p-6">
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  {isGuest ? 'Welcome, Guest!' : 'Welcome to AI Chatbot'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {isGuest
                    ? `You have ${guestRemainingRequests} free messages to try our AI assistant. Ask questions, get recommendations, or just chat!`
                    : 'Start a conversation with our AI assistant. Ask questions, get recommendations, or just chat!'
                  }
                </p>
                {isGuest && (
                  <div className="mb-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg border border-indigo-200 dark:border-indigo-700">
                    <p className="text-sm text-indigo-700 dark:text-indigo-300 mb-2">
                      🎉 Enjoying the experience?
                    </p>
                    <a
                      href="/auth/signup"
                      className="inline-flex items-center gap-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                    >
                      Sign up for unlimited access
                    </a>
                  </div>
                )}
                <div className="grid grid-cols-1 gap-3 text-sm">
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-blue-700 dark:text-blue-300">
                    💡 Try asking: "What's the weather like today?"
                  </div>
                  <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg text-green-700 dark:text-green-300">
                    🎯 Or: "Help me plan my day"
                  </div>
                  <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg text-purple-700 dark:text-purple-300">
                    🤖 Or: "Tell me a joke"
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-0">
              {messages.map((message, index) => (
                <ChatMessage key={index} message={message} />
              ))}
              {isTyping && (
                <ChatMessage
                  message={{
                    role: 'assistant',
                    content: '',
                    timestamp: new Date(),
                  }}
                  isTyping={true}
                />
              )}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* Guest Banner */}
        {isGuest && (
          <GuestBanner
            remainingMessages={guestRemainingRequests}
            maxMessages={10}
          />
        )}

        {/* Input */}
        <ChatInput
          onSendMessage={sendMessage}
          disabled={isLoading || (isGuest && guestRemainingRequests <= 0)}
          placeholder={
            isGuest && guestRemainingRequests <= 0
              ? "Sign up to continue chatting..."
              : isLoading
                ? "AI is thinking..."
                : "Type your message..."
          }
        />
      </div>
    </div>
  );
}
