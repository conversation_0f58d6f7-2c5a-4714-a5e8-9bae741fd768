/// <reference types="node" resolution-mode="require"/>
/// <reference types="node" resolution-mode="require"/>
/// <reference types="node" resolution-mode="require"/>
import { type Stats } from 'fs';
import { Minipass } from 'minipass';
import { Header } from './header.js';
import { TarOptions, TarOptionsWithAliases } from './options.js';
import { ReadEntry } from './read-entry.js';
import { EntryTypeName } from './types.js';
import { WarnData, Warner, WarnEvent } from './warn-method.js';
declare const PROCESS: unique symbol;
declare const FILE: unique symbol;
declare const DIRECTORY: unique symbol;
declare const SYMLINK: unique symbol;
declare const HARDLINK: unique symbol;
declare const HEADER: unique symbol;
declare const READ: unique symbol;
declare const LSTAT: unique symbol;
declare const ONLSTAT: unique symbol;
declare const ONREAD: unique symbol;
declare const ONREADLINK: unique symbol;
declare const OPENFILE: unique symbol;
declare const ONOPENFILE: unique symbol;
declare const CLOSE: unique symbol;
declare const MODE: unique symbol;
declare const AWAITDRAIN: unique symbol;
declare const ONDRAIN: unique symbol;
declare const PREFIX: unique symbol;
export declare class WriteEntry extends Minipass<Buffer, Minipass.ContiguousData, WarnEvent> implements Warner {
    #private;
    path: string;
    portable: boolean;
    myuid: number;
    myuser: string;
    maxReadSize: number;
    linkCache: Exclude<TarOptions['linkCache'], undefined>;
    statCache: Exclude<TarOptions['statCache'], undefined>;
    preservePaths: boolean;
    cwd: string;
    strict: boolean;
    mtime?: Date;
    noPax: boolean;
    noMtime: boolean;
    prefix?: string;
    fd?: number;
    blockLen: number;
    blockRemain: number;
    buf?: Buffer;
    pos: number;
    remain: number;
    length: number;
    offset: number;
    win32: boolean;
    absolute: string;
    header?: Header;
    type?: EntryTypeName | 'Unsupported';
    linkpath?: string;
    stat?: Stats;
    onWriteEntry?: (entry: WriteEntry) => any;
    constructor(p: string, opt_?: TarOptionsWithAliases);
    warn(code: string, message: string | Error, data?: WarnData): void;
    emit(ev: keyof WarnEvent, ...data: any[]): boolean;
    [LSTAT](): void;
    [ONLSTAT](stat: Stats): void;
    [PROCESS](): void | this;
    [MODE](mode: number): number;
    [PREFIX](path: string): string;
    [HEADER](): void;
    [DIRECTORY](): void;
    [SYMLINK](): void;
    [ONREADLINK](linkpath: string): void;
    [HARDLINK](linkpath: string): void;
    [FILE](): void | this;
    [OPENFILE](): void;
    [ONOPENFILE](fd: number): void;
    [READ](): void;
    [CLOSE](cb?: (er?: null | Error | NodeJS.ErrnoException) => any): void;
    [ONREAD](bytesRead: number): void;
    [AWAITDRAIN](cb: () => any): void;
    write(buffer: Buffer | string, cb?: () => void): boolean;
    write(str: Buffer | string, encoding?: BufferEncoding | null, cb?: () => void): boolean;
    [ONDRAIN](): void;
}
export declare class WriteEntrySync extends WriteEntry implements Warner {
    sync: true;
    [LSTAT](): void;
    [SYMLINK](): void;
    [OPENFILE](): void;
    [READ](): void;
    [AWAITDRAIN](cb: () => any): void;
    [CLOSE](cb?: (er?: null | Error | NodeJS.ErrnoException) => any): void;
}
export declare class WriteEntryTar extends Minipass<Buffer, Buffer | string, WarnEvent> implements Warner {
    blockLen: number;
    blockRemain: number;
    buf: number;
    pos: number;
    remain: number;
    length: number;
    preservePaths: boolean;
    portable: boolean;
    strict: boolean;
    noPax: boolean;
    noMtime: boolean;
    readEntry: ReadEntry;
    type: EntryTypeName;
    prefix?: string;
    path: string;
    mode?: number;
    uid?: number;
    gid?: number;
    uname?: string;
    gname?: string;
    header?: Header;
    mtime?: Date;
    atime?: Date;
    ctime?: Date;
    linkpath?: string;
    size: number;
    onWriteEntry?: (entry: WriteEntry) => any;
    warn(code: string, message: string | Error, data?: WarnData): void;
    constructor(readEntry: ReadEntry, opt_?: TarOptionsWithAliases);
    [PREFIX](path: string): string;
    [MODE](mode: number): number;
    write(buffer: Buffer | string, cb?: () => void): boolean;
    write(str: Buffer | string, encoding?: BufferEncoding | null, cb?: () => void): boolean;
    end(cb?: () => void): this;
    end(chunk: Buffer | string, cb?: () => void): this;
    end(chunk: Buffer | string, encoding?: BufferEncoding, cb?: () => void): this;
}
export {};
//# sourceMappingURL=write-entry.d.ts.map