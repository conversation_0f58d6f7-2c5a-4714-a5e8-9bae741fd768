import { NextRequest, NextResponse } from 'next/server';
import { OpenAIService, ChatMessage } from '@/lib/openai';

// Rate limiting for guest users (simpler in-memory approach)
const guestRateLimit = new Map<string, { count: number; resetTime: number }>();
const GUEST_MAX_REQUESTS = 10; // Lower limit for guests
const GUEST_WINDOW_MS = 60 * 60 * 1000; // 1 hour

function checkGuestRateLimit(ip: string): { allowed: boolean; remaining: number } {
  const now = Date.now();
  const userLimit = guestRateLimit.get(ip);

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new limit
    guestRateLimit.set(ip, {
      count: 1,
      resetTime: now + GUEST_WINDOW_MS,
    });
    return { allowed: true, remaining: GUEST_MAX_REQUESTS - 1 };
  }

  if (userLimit.count >= GUEST_MAX_REQUESTS) {
    return { allowed: false, remaining: 0 };
  }

  userLimit.count++;
  return { allowed: true, remaining: GUEST_MAX_REQUESTS - userLimit.count };
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';

    // Check rate limit for guest users
    const rateLimitResult = checkGuestRateLimit(ip);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded. Please try again later or sign up for unlimited access.',
          isGuest: true
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Remaining': '0',
          }
        }
      );
    }

    // Parse request body
    const { message, conversationHistory = [] } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    // Limit conversation history for guests (last 10 messages)
    const limitedHistory = conversationHistory.slice(-10);

    // Convert to OpenAI format
    const contextMessages: ChatMessage[] = limitedHistory.map((msg: any) => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
    }));

    // Add current user message
    contextMessages.push({
      role: 'user',
      content: message,
    });

    // Generate AI response with guest settings
    const chatOptions = {
      tone: 'friendly' as const, // Default to friendly for guests
      language: 'en' as const,   // Default to English for guests
      maxTokens: 500,            // Limit response length for guests
    };

    const aiResponse = await OpenAIService.generateResponse(contextMessages, chatOptions);

    return NextResponse.json({
      response: aiResponse,
      isGuest: true,
      remaining: rateLimitResult.remaining,
      maxRequests: GUEST_MAX_REQUESTS,
    }, {
      headers: {
        'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
      },
    });
  } catch (error) {
    console.error('Guest chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', isGuest: true },
      { status: 500 }
    );
  }
}
