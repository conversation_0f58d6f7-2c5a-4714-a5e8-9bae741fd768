@tailwind base;
@tailwind components;
@tailwind utilities;
/* ChatGPT-like color scheme */
:root {
  /* Light mode colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f7f7f8;
  --bg-tertiary: #ececf1;
  --text-primary: #0d0d0d;
  --text-secondary: #676767;
  --text-tertiary: #8e8ea0;
  --border-light: #e5e5e5;
  --border-medium: #d1d5db;
  --accent-primary: #10a37f;
  --accent-hover: #0d8f6f;
  --accent-light: #f0fdf4;
  --danger: #ef4444;
  --warning: #f59e0b;
}

[data-theme="dark"] {
  /* Dark mode colors */
  --bg-primary: #212121;
  --bg-secondary: #2f2f2f;
  --bg-tertiary: #424242;
  --text-primary: #ececec;
  --text-secondary: #c5c5d2;
  --text-tertiary: #8e8ea0;
  --border-light: #4d4d4f;
  --border-medium: #565869;
  --accent-primary: #10a37f;
  --accent-hover: #0d8f6f;
  --accent-light: #064e3b;
  --danger: #ef4444;
  --warning: #f59e0b;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  height: 100%;
  overflow-x: hidden;
}

/* Custom scrollbar - ChatGPT style */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--border-medium);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-tertiary);
}

/* Smooth transitions */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Focus styles */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Message typing animation */
@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.typing-dot {
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}
