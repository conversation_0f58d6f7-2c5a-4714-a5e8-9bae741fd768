'use client';

import Link from 'next/link';
import { Spark<PERSON>, ArrowR<PERSON>, Users } from 'lucide-react';

interface GuestBannerProps {
  remainingMessages: number;
  maxMessages: number;
}

export default function GuestBanner({ remainingMessages, maxMessages }: GuestBannerProps) {
  const usedMessages = maxMessages - remainingMessages;
  const progressPercentage = (usedMessages / maxMessages) * 100;

  return (
    <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border-t border-indigo-200 dark:border-indigo-700 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center">
              <Sparkles className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Guest Mode
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {remainingMessages} of {maxMessages} messages remaining
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Link
              href="/auth/signin"
              className="text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium text-sm transition-colors"
            >
              Sign In
            </Link>
            <Link
              href="/auth/signup"
              className="group bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2"
            >
              <Users className="h-4 w-4" />
              Sign Up Free
              <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-3">
          <div 
            className="bg-gradient-to-r from-indigo-600 to-purple-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
        
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>Free trial</span>
          <span>{usedMessages}/{maxMessages} used</span>
        </div>
        
        {remainingMessages <= 2 && (
          <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              ⚠️ Only {remainingMessages} messages left! 
              <Link href="/auth/signup" className="font-medium underline ml-1">
                Sign up now
              </Link> for unlimited access.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
